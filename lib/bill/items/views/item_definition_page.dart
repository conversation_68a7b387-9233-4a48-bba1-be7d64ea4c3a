import 'package:flutter/material.dart';

class ItemDefinitionPage extends StatelessWidget {
  const ItemDefinitionPage({
    super.key,
    required this.itemList,
    required this.tip,
    required this.addItemButton,
    required this.total,
    required this.continueButton,
    required this.formKey,
  });

  final Widget itemList;
  final Widget tip;
  final Widget addItemButton;
  final Widget total;
  final Widget continueButton;
  final GlobalKey<FormState> formKey;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: BottomAppBar(
        elevation: 0,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [addItemButton, total, continueButton],
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  itemList,
                  const Divider(height: 60),
                  tip,
                  const SizedBox(height: 30),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
