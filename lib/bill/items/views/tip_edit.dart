import 'package:flutter/material.dart';

import '../../../components/button/delete_button.dart';
import '../../../components/card/list_item_card.dart';
import '../../../components/input/form_text_field.dart';

class TipEdit extends StatelessWidget {
  const TipEdit({
    super.key,
    required this.tipLabel,
    required this.percentageLabel,
    required this.amountLabel,
    required this.percentageTextValue,
    required this.amountTextValue,
    required this.onPercentageChanged,
    required this.onAmountChanged,
    this.percentageValidator,
    this.amountValidator,
    required this.onDeletePressed,
    this.autovalidateMode = AutovalidateMode.disabled,
  });

  final String tipLabel;
  final String percentageLabel;
  final String amountLabel;
  final String percentageTextValue;
  final String amountTextValue;
  final void Function(String percentage) onPercentageChanged;
  final void Function(String amount) onAmountChanged;
  final String? Function(String?)? percentageValidator;
  final String? Function(String?)? amountValidator;
  final void Function() onDeletePressed;
  final AutovalidateMode autovalidateMode;

  @override
  Widget build(BuildContext context) {
    return ListItemCard(
      child: Column(
        children: [
          // Header
          IntrinsicHeight(
            child: Row(
              children: [
                // Label
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Theme.of(context).colorScheme.outline,
                      ),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Center(child: Text(tipLabel)),
                  ),
                ),

                // Delete button
                DeleteButton(onPressed: onDeletePressed),
              ],
            ),
          ),

          // Body
          const SizedBox(height: 12),
          Row(
            children: [
              // Percentage
              Flexible(
                child: FormTextField.integer(
                  prefixIcon: Icons.percent,
                  label: percentageLabel,
                  initialValue: percentageTextValue,
                  onChanged: onPercentageChanged,
                  validator: percentageValidator,
                  autovalidateMode: autovalidateMode,
                ),
              ),

              // Amount
              const SizedBox(width: 8),
              Flexible(
                child: FormTextField.currency(
                  label: amountLabel,
                  initialValue: amountTextValue,
                  onChanged: onAmountChanged,
                  validator: amountValidator,
                  autovalidateMode: autovalidateMode,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
